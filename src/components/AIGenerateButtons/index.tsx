'use client';

import React, { useState, useRef } from 'react';
import { useModuleStore } from '@/store/useModuleStore';
import { useUserStore } from '@/store/useUserStore';
import { aiApi, type PromptType, type ResumeModule } from '@/api/client';
import MarkdownViewer from '@/components/MarkdownViewer';
import AIHistoryDrawer from '@/components/AIHistoryDrawer';
import LoginDialog from '@/components/Auth/LoginDialog';
import { toast } from 'sonner';
import './ai-markdown.css';

interface AIGenerateButtonsProps {
  className?: string;
  showAnalysisResult?: boolean;
  onRefresh?: () => void;
  onUseResult?: () => void;
  /** 富文本内容，用作AI生成的描述 (已废弃，请使用markdownContent) */
  richTextContent?: string;
  /** Markdown内容，用作AI生成的描述 */
  markdownContent?: string;
  /** 生成内容的回调函数 */
  onContentGenerated?: (content: string) => void;
  /** 教育经历相关字段 - 用于AI生成验证 */
  educationInfo?: {
    school_name?: string;
    major?: string;
    degree?: string;
  };
  /** 工作经历相关字段 - 用于AI生成验证 */
  workInfo?: {
    company?: string;
    job?: string;
    department?: string;
    start_month?: string;
    end_month?: string;
  };
  /** 项目经历相关字段 - 用于AI生成验证 */
  projectInfo?: {
    name?: string;
    role?: string;
    company?: string;
  };
  /** 个人总结相关字段 - 用于AI生成验证 */
  personalSummaryInfo?: {
    job?: string;
    workYears?: number;
  };
  /** 研究经历相关字段 - 用于AI生成验证 */
  researchInfo?: {
    name?: string;
    role?: string;
    department?: string;
    start_month?: string;
    end_month?: string;
  };
  /** 社团经历相关字段 - 用于AI生成验证 */
  teamInfo?: {
    name?: string;
    department?: string;
    role?: string;
    start_month?: string;
    end_month?: string;
  };
}

/**
 * AI生成按钮组件
 * 包含AI生成、AI改写和历史记录按钮
 */
const AIGenerateButtons: React.FC<AIGenerateButtonsProps> = ({
  className = '',
  showAnalysisResult = false,
  onRefresh,
  onUseResult,
  richTextContent = '',
  markdownContent = '',
  onContentGenerated,
  educationInfo,
  workInfo,
  projectInfo,
  personalSummaryInfo,
  researchInfo,
  teamInfo
}) => {
  // 获取store中的数据
  const { activeModuleId, resumeId } = useModuleStore();
  const { is_logged_in } = useUserStore();

  // AI生成状态
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState('');
  const [currentAbortFn, setCurrentAbortFn] = useState<(() => void) | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [lastPromptType, setLastPromptType] = useState<PromptType | null>(null);

  // 历史记录状态
  const [showHistory, setShowHistory] = useState(false);

  // 登录对话框状态
  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false);

  // 使用ref来跟踪最新的生成内容
  const generatedContentRef = useRef('');

  // 使用ref来引用结果区域，用于滚动
  const resultAreaRef = useRef<HTMLDivElement>(null);

  // 添加按钮点击动画状态
  const [clickedButton, setClickedButton] = useState<string | null>(null);

  // 添加容器震动动画状态（可选）
  const [isShaking, setIsShaking] = useState(false);

  // 将模块ID映射为API需要的模块名称
  const getModuleName = (moduleId: string | null): ResumeModule => {
    if (!moduleId) return 'personal_summary'; // 默认值

    // 映射关系
    const moduleMap: Record<string, ResumeModule> = {
      'basic_info': 'basic_info',
      'education': 'education',
      'work': 'work',
      'project': 'project',
      'research': 'research',
      'team': 'team',
      'portfolio': 'portfolio',
      'other': 'other',
      'personal_summary': 'personal_summary',
      'honors': 'honors',
      'skills': 'skills',
      'custom_modules': 'custom_modules',
      'slogan': 'slogan',
      'resume_style': 'resume_style'
    };

    return moduleMap[moduleId] || 'personal_summary';
  };

  // 计算工作年限
  const calculateWorkYears = (startMonth: string, endMonth: string): number => {
    if (!startMonth || !endMonth) return 0;

    try {
      const start = new Date(startMonth + '-01');
      const end = endMonth === '至今' ? new Date() : new Date(endMonth + '-01');

      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffYears = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 365));

      return diffYears;
    } catch {
      return 0;
    }
  };

  // 生成工作经历AI文案
  const generateWorkPrompt = (promptType: PromptType): string => {
    if (!workInfo) return markdownContent || richTextContent || '请生成相关内容';

    const { company = '', job = '', department = '', start_month = '', end_month = '' } = workInfo;
    const workYears = calculateWorkYears(start_month, end_month);

    if (promptType === 'generate') {
      // AI生成模式
      if (markdownContent && markdownContent.trim().length > 0) {
        // 文案2：在编辑框中填内容的
        return `公司：${company}，部门：${department}，职位：${job}，工作${workYears}年。现在写个人求职简历，请你以行业资深HR的视角，根据"${markdownContent}"给我写一下工作经历。不能超过300字，文本形式，要有格式，我可以直接用。

工作经历要求：
公司、部门、职位、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
工作经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      } else {
        // 文案1：没有在编辑框中填内容的
        return `公司：${company}，部门：${department}，职位：${job}，工作${workYears}年。现在写个人求职简历，请你以行业资深HR的视角给我写一下工作经历。不能超过300字，文本形式，要有格式，我可以直接用。

工作经历要求：
公司、部门、职位、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
工作经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      }
    } else if (promptType === 'continue') {
      // AI续写模式
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `公司：${company}，部门：${department}，职位：${job}，工作${workYears}年。现在写个人求职简历，请你以行业资深HR的视角，基于我已有的工作经历内容进行续写补充，让内容更完整。不能超过300字，文本形式，要有格式，我可以直接用。

我已有的工作经历内容：
${markdownContent}

续写要求：
公司、部门、职位、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
工作经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    } else if (promptType === 'professional') {
      // AI改写模式 - 更专业
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${job}职位工作经历改的更专业：让经历更具说服力。要求不能超过300字。

我的工作经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    } else if (promptType === 'concise') {
      // AI改写模式 - 更凝练
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${job}职位工作经历改的更凝练：让经历更简洁有力。要求不能超过300字。

我的工作经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    } else if (promptType === 'detailed') {
      // AI改写模式 - 更详细
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${job}职位工作经历改的更详细：让经历更丰富完整。要求不能超过300字。

我的工作经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    }

    return markdownContent || richTextContent || '请生成相关内容';
  };

  // 生成项目经历AI文案
  const generateProjectPrompt = (promptType: PromptType): string => {
    if (!projectInfo) return markdownContent || richTextContent || '请生成相关内容';

    const { name = '', role = '', company = '' } = projectInfo;

    if (promptType === 'generate') {
      // AI生成模式
      if (markdownContent && markdownContent.trim().length > 0) {
        // 文案2：在编辑框中填内容的
        return `公司：${company}，项目名称：${name}，我的角色：${role}。现在写个人求职简历，请你以行业资深HR的视角，根据"${markdownContent}"给我写一下项目经历。不能超过300字，文本形式，要有格式，我可以直接用。

项目经历要求：
公司、项目名称、角色、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
项目经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      } else {
        // 文案1：没有在编辑框中填内容的
        return `公司：${company}，项目名称：${name}，我的角色：${role}。现在写个人求职简历，请你以行业资深HR的视角给我写一下项目经历。不能超过300字，文本形式，要有格式，我可以直接用。

项目经历要求：
公司、项目名称、角色、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
项目经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      }
    } else if (promptType === 'continue') {
      // AI续写模式
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `公司：${company}，项目名称：${name}，我的角色：${role}。现在写个人求职简历，请你以行业资深HR的视角，基于我已有的项目经历内容进行续写补充，让内容更完整。不能超过300字，文本形式，要有格式，我可以直接用。

我已有的项目经历内容：
${markdownContent}

续写要求：
公司、项目名称、角色、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
项目经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    } else if (promptType === 'professional') {
      // AI改写模式 - 更专业
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${name}项目经历改的更专业：让经历更具说服力。要求不能超过300字。

我的项目经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    } else if (promptType === 'concise') {
      // AI改写模式 - 更凝练
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${name}项目经历改的更凝练：让经历更简洁有力。要求不能超过300字。

我的项目经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    } else if (promptType === 'detailed') {
      // AI改写模式 - 更详细
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${name}项目经历改的更详细：让经历更丰富完整。要求不能超过300字。

我的项目经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    }

    return markdownContent || richTextContent || '请生成相关内容';
  };

  // 生成个人总结AI文案
  const generatePersonalSummaryPrompt = (promptType: PromptType): string => {
    if (!personalSummaryInfo) return markdownContent || richTextContent || '请生成相关内容';

    const { job = '', workYears = 0 } = personalSummaryInfo;

    if (promptType === 'generate') {
      // AI生成模式
      if (markdownContent && markdownContent.trim().length > 0) {
        // 文案2：在编辑框中填内容的
        return `职位：${job}，工作经验：${workYears}年。现在写个人求职简历，请你以行业资深HR的视角，根据"${markdownContent}"给我写一下个人总结。不能超过200字，文本形式，要有格式，我可以直接用。

个人总结要求：
职位、工作经验、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
个人总结不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      } else {
        // 文案1：没有在编辑框中填内容的
        return `职位：${job}，工作经验：${workYears}年。现在写个人求职简历，请你以行业资深HR的视角给我写一下个人总结。不能超过200字，文本形式，要有格式，我可以直接用。

个人总结要求：
职位、工作经验、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
个人总结不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      }
    } else if (promptType === 'continue') {
      // AI续写模式
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `职位：${job}，工作经验：${workYears}年。现在写个人求职简历，请你以行业资深HR的视角，基于我已有的个人总结内容进行续写补充，让内容更完整。不能超过200字，文本形式，要有格式，我可以直接用。

我已有的个人总结内容：
${markdownContent}

续写要求：
职位、工作经验、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
个人总结不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    } else if (promptType === 'professional') {
      // AI改写模式 - 更专业
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${job}职位个人总结改的更专业：让总结更具说服力。要求不能超过200字。

我的个人总结内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    } else if (promptType === 'concise') {
      // AI改写模式 - 更凝练
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${job}职位个人总结改的更凝练：让总结更简洁有力。要求不能超过200字。

我的个人总结内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    } else if (promptType === 'detailed') {
      // AI改写模式 - 更详细
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${job}职位个人总结改的更详细：让总结更丰富完整。要求不能超过200字。

我的个人总结内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    }

    return markdownContent || richTextContent || '请生成相关内容';
  };

  // 生成研究经历AI文案
  const generateResearchPrompt = (promptType: PromptType): string => {
    if (!researchInfo) return markdownContent || richTextContent || '请生成相关内容';

    const { name = '', role = '', department = '', start_month = '', end_month = '' } = researchInfo;
    const workYears = calculateWorkYears(start_month, end_month);

    if (promptType === 'generate') {
      // AI生成模式
      if (markdownContent && markdownContent.trim().length > 0) {
        // 文案2：在编辑框中填内容的
        return `机构：${department}，研究课题/方向：${name}，我的角色：${role}，研究${workYears}年。现在写个人求职简历，请你以行业资深HR的视角，根据"${markdownContent}"给我写一下研究经历。不能超过300字，文本形式，要有格式，我可以直接用。

研究经历要求：
机构、研究课题/方向、角色、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
研究经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      } else {
        // 文案1：没有在编辑框中填内容的
        return `机构：${department}，研究课题/方向：${name}，我的角色：${role}，研究${workYears}年。现在写个人求职简历，请你以行业资深HR的视角给我写一下研究经历。不能超过300字，文本形式，要有格式，我可以直接用。

研究经历要求：
机构、研究课题/方向、角色、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
研究经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      }
    } else if (promptType === 'continue') {
      // AI续写模式
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `机构：${department}，研究课题/方向：${name}，我的角色：${role}，研究${workYears}年。现在写个人求职简历，请你以行业资深HR的视角，基于我已有的研究经历内容进行续写补充，让内容更完整。不能超过300字，文本形式，要有格式，我可以直接用。

我已有的研究经历内容：
${markdownContent}

续写要求：
机构、研究课题/方向、角色、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
研究经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    } else if (promptType === 'professional') {
      // AI改写模式 - 更专业
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${name}研究经历改的更专业：让经历更具说服力。要求不能超过300字。

我的研究经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    } else if (promptType === 'concise') {
      // AI改写模式 - 更凝练
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${name}研究经历改的更凝练：让经历更简洁有力。要求不能超过300字。

我的研究经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    } else if (promptType === 'detailed') {
      // AI改写模式 - 更详细
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${name}研究经历改的更详细：让经历更丰富完整。要求不能超过300字。

我的研究经历内容：
${markdownContent}

格式要求：
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。`;
    }

    return markdownContent || richTextContent || '请生成相关内容';
  };

  // 生成社团经历AI文案
  const generateTeamPrompt = (promptType: PromptType): string => {
    if (!teamInfo) return markdownContent || richTextContent || '请生成相关内容';

    const { name = '', department = '', role = '', start_month = '', end_month = '' } = teamInfo;
    const workYears = calculateWorkYears(start_month, end_month);

    if (promptType === 'generate') {
      // AI生成模式
      if (markdownContent && markdownContent.trim().length > 0) {
        // 文案2：在编辑框中填内容的
        return `我在${name}，所在部门：${department}，我的角色：${role}，参与${workYears}年。你作为我未来的直属领导，结合行业资深HR的角度，按照行业的招聘要求。根据"${markdownContent}"，给我写一下社团经历。不能超过300字，文本形式、要分行，我可以直接用。
社团经历要求：
简历结构清晰，便于阅读
简历要有核心竞争力
社团经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      } else {
        // 文案1：没有在编辑框中填内容的
        return `我在${name}，所在部门：${department}，我的角色：${role}，参与${workYears}年。你作为我未来的直属领导，结合行业资深HR的角度，按照行业的招聘要求，给我写一下社团经历。不能超过300字，文本形式、要分行，我可以直接用。
社团经历要求：
简历结构清晰，便于阅读
简历要有核心竞争力
社团经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      }
    } else if (promptType === 'continue') {
      // AI续写模式
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `我在${name}，所在部门：${department}，我的角色：${role}，参与${workYears}年。你作为我未来的直属领导，结合行业资深HR的角度，按照行业的招聘要求，基于我已有的社团经历内容进行续写补充，让内容更完整。不能超过300字，文本形式、要分行，我可以直接用。

我已有的社团经历内容：
${markdownContent}

续写要求：
简历结构清晰，便于阅读
简历要有核心竞争力
社团经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    } else if (promptType === 'professional') {
      // AI改写模式 - 更专业
      if (!markdownContent || markdownContent.trim().length < 30) {
        toast.error('AI改写需要至少30个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `我在${name}，所在部门：${department}，我的角色：${role}，参与${workYears}年。你作为我未来的直属领导，结合行业资深HR的角度，按照行业的招聘要求。根据以下内容，把我的社团经历写的更专业。不能超过300字，文本形式、要分行，我可以直接用。

我的社团经历内容：
${markdownContent}

社团经历要求：
简历结构清晰，便于阅读
简历要有核心竞争力
社团经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    } else if (promptType === 'concise') {
      // AI改写模式 - 更凝练
      if (!markdownContent || markdownContent.trim().length < 30) {
        toast.error('AI改写需要至少30个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `我在${name}，所在部门：${department}，我的角色：${role}，参与${workYears}年。你作为我未来的直属领导，结合行业资深HR的角度，按照行业的招聘要求。根据以下内容，把我的社团经历写的更凝练。不能超过300字，文本形式、要分行，我可以直接用。

我的社团经历内容：
${markdownContent}

社团经历要求：
简历结构清晰，便于阅读
简历要有核心竞争力
社团经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    } else if (promptType === 'detailed') {
      // AI改写模式 - 更详细
      if (!markdownContent || markdownContent.trim().length < 30) {
        toast.error('AI改写需要至少30个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `我在${name}，所在部门：${department}，我的角色：${role}，参与${workYears}年。你作为我未来的直属领导，结合行业资深HR的角度，按照行业的招聘要求。根据以下内容，把我的社团经历写的更详细。不能超过300字，文本形式、要分行，我可以直接用。

我的社团经历内容：
${markdownContent}

社团经历要求：
简历结构清晰，便于阅读
简历要有核心竞争力
社团经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    }

    return markdownContent || richTextContent || '请生成相关内容';
  };

  // 生成教育经历AI文案
  const generateEducationPrompt = (promptType: PromptType): string => {
    if (!educationInfo) return markdownContent || richTextContent || '请生成相关内容';

    const { school_name = '', major = '', degree = '' } = educationInfo;

    if (promptType === 'generate') {
      // AI生成模式
      if (markdownContent && markdownContent.trim().length > 0) {
        // 文案2：在编辑框中填内容的
        return `学校：${school_name}，专业：${major}，学历：${degree}。现在写个人求职简历，请你以行业资深HR的视角，根据"${markdownContent}"给我写一下教育经历。不能超过200字，文本形式，要有格式，我可以直接用。

教育经历要求：
学校、专业、学历、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
教育经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      } else {
        // 文案1：没有在编辑框中填内容的
        return `学校：${school_name}，专业：${major}，学历：${degree}。现在写个人求职简历，请你以行业资深HR的视角给我写一下教育经历。不能超过200字，文本形式，要有格式，我可以直接用。

教育经历要求：
学校、专业、学历、时间不要返回给我。
无序列表格式，每行以星号和空格开头。
结构清晰，便于阅读。
要有核心竞争力。
教育经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
      }
    } else if (promptType === 'continue') {
      // AI续写模式
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `学校：${school_name}，专业：${major}，学历：${degree}。现在写个人求职简历，请你以行业资深HR的视角，基于我已有的教育经历内容进行续写补充，让内容更完整。不能超过200字，文本形式，要有格式，我可以直接用。

我已有的教育经历内容：
${markdownContent}

续写要求：
学校、专业、学历、时间不要返回给我。
markdown无序列表的方式。
结构清晰，便于阅读。
要有核心竞争力。
教育经历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。`;
    } else if (promptType === 'professional') {
      // AI改写模式 - 更专业
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${major}专业教育经历改的更专业：让经历更具说服力。要求不能超过300字。

我的教育经历内容：
${markdownContent}`;
    } else if (promptType === 'concise') {
      // AI改写模式 - 更凝练
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${major}专业教育经历改的更凝练：让经历更简洁有力。要求不能超过300字。

我的教育经历内容：
${markdownContent}`;
    } else if (promptType === 'detailed') {
      // AI改写模式 - 更详细
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return '';
      }
      return `从行业资深HR的视角、把以下${major}专业教育经历改的更详细：让经历更丰富完整。要求不能超过300字。

我的教育经历内容：
${markdownContent}`;
    }

    return markdownContent || richTextContent || '请生成相关内容';
  };

  // 验证教育经历AI生成的必填字段
  const validateEducationGenerate = (): boolean => {
    if (activeModuleId === 'education' && educationInfo) {
      const { school_name = '', major = '', degree = '' } = educationInfo;

      if (!school_name.trim() || !major.trim() || !degree.trim()) {
        toast.error('AI生成需要填写学校名称、专业和学历', {
          position: 'top-center'
        });
        return false;
      }
    }
    return true;
  };

  // 验证工作经历AI生成的必填字段
  const validateWorkGenerate = (): boolean => {
    if (activeModuleId === 'work' && workInfo) {
      const { company = '', job = '', department = '', start_month = '', end_month = '' } = workInfo;

      if (!company.trim() || !job.trim() || !department.trim() || !start_month.trim() || !end_month.trim()) {
        toast.error('AI生成需要填写公司、职位名称、所在部门和时间', {
          position: 'top-center'
        });
        return false;
      }
    }
    return true;
  };

  // 验证项目经历AI生成的必填字段
  const validateProjectGenerate = (): boolean => {
    if (activeModuleId === 'project' && projectInfo) {
      const { name = '', role = '', company = '' } = projectInfo;

      if (!name.trim() || !role.trim() || !company.trim()) {
        toast.error('AI生成需要填写项目名称、我的角色和所属公司', {
          position: 'top-center'
        });
        return false;
      }
    }
    return true;
  };

  // 验证个人总结AI生成的必填字段
  const validatePersonalSummaryGenerate = (): boolean => {
    if (activeModuleId === 'personal_summary' && personalSummaryInfo) {
      const { job = '', workYears = 0 } = personalSummaryInfo;

      if (!job.trim() || workYears === 0) {
        toast.error('AI生成需要填写工作类型和年限', {
          position: 'top-center'
        });
        return false;
      }
    }
    return true;
  };

  // 验证研究经历AI生成的必填字段
  const validateResearchGenerate = (): boolean => {
    if (activeModuleId === 'research' && researchInfo) {
      const { name = '', role = '', department = '', start_month = '', end_month = '' } = researchInfo;

      if (!name.trim() || !role.trim() || !department.trim() || !start_month.trim() || !end_month.trim()) {
        toast.error('AI生成需要填写研究课题/方向、你的角色、所在公司/组织/团队和时间', {
          position: 'top-center'
        });
        return false;
      }
    }
    return true;
  };

  // 验证社团经历AI生成的必填字段
  const validateTeamGenerate = (): boolean => {
    if (activeModuleId === 'team' && teamInfo) {
      const { name = '', department = '', role = '', start_month = '', end_month = '' } = teamInfo;

      if (!name.trim() || !department.trim() || !role.trim() || !start_month.trim() || !end_month.trim()) {
        toast.error('AI生成需要填写组织/活动名称、所在部门、你的角色和时间', {
          position: 'top-center'
        });
        return false;
      }
    }
    return true;
  };

  // 处理AI生成
  const handleAIGenerate = async (promptType: PromptType) => {
    if (!resumeId || isGenerating) return;

    // 检查登录状态
    if (!is_logged_in) {
      toast.error('请先登录后再使用AI功能', {
        position: 'top-center'
      });
      setIsLoginDialogOpen(true);
      return;
    }

    // 教育经历模块的特殊验证
    if (activeModuleId === 'education' && promptType === 'generate') {
      if (!validateEducationGenerate()) {
        return;
      }
    }

    // AI续写的验证（针对教育经历）
    if (activeModuleId === 'education' && promptType === 'continue') {
      // 验证必填字段
      if (!validateEducationGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // AI改写的验证（针对教育经历）
    if (activeModuleId === 'education' && (promptType === 'professional' || promptType === 'concise' || promptType === 'detailed')) {
      // 验证必填字段
      if (!validateEducationGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI改写需要至少20个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // 工作经历模块的特殊验证
    if (activeModuleId === 'work' && promptType === 'generate') {
      if (!validateWorkGenerate()) {
        return;
      }
    }

    // AI续写的验证（针对工作经历）
    if (activeModuleId === 'work' && promptType === 'continue') {
      // 验证必填字段
      if (!validateWorkGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // AI改写的验证（针对工作经历）
    if (activeModuleId === 'work' && (promptType === 'professional' || promptType === 'concise' || promptType === 'detailed')) {
      // 验证必填字段
      if (!validateWorkGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 30) {
        toast.error('AI改写需要至少30个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // 项目经历模块的特殊验证
    if (activeModuleId === 'project' && promptType === 'generate') {
      if (!validateProjectGenerate()) {
        return;
      }
    }

    // AI续写的验证（针对项目经历）
    if (activeModuleId === 'project' && promptType === 'continue') {
      // 验证必填字段
      if (!validateProjectGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // AI改写的验证（针对项目经历）
    if (activeModuleId === 'project' && (promptType === 'professional' || promptType === 'concise' || promptType === 'detailed')) {
      // 验证必填字段
      if (!validateProjectGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 30) {
        toast.error('AI改写需要至少30个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // 个人总结模块的特殊验证
    if (activeModuleId === 'personal_summary' && promptType === 'generate') {
      if (!validatePersonalSummaryGenerate()) {
        return;
      }
    }

    // AI续写的验证（针对个人总结）
    if (activeModuleId === 'personal_summary' && promptType === 'continue') {
      // 验证必填字段
      if (!validatePersonalSummaryGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // AI改写的验证（针对个人总结）
    if (activeModuleId === 'personal_summary' && (promptType === 'professional' || promptType === 'concise' || promptType === 'detailed')) {
      // 验证必填字段
      if (!validatePersonalSummaryGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 30) {
        toast.error('AI改写需要至少30个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // 研究经历模块的特殊验证
    if (activeModuleId === 'research' && promptType === 'generate') {
      if (!validateResearchGenerate()) {
        return;
      }
    }

    // AI续写的验证（针对研究经历）
    if (activeModuleId === 'research' && promptType === 'continue') {
      // 验证必填字段
      if (!validateResearchGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // AI改写的验证（针对研究经历）
    if (activeModuleId === 'research' && (promptType === 'professional' || promptType === 'concise' || promptType === 'detailed')) {
      // 验证必填字段
      if (!validateResearchGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 30) {
        toast.error('AI改写需要至少30个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // 社团经历模块的特殊验证
    if (activeModuleId === 'team' && promptType === 'generate') {
      if (!validateTeamGenerate()) {
        return;
      }
    }

    // AI续写的验证（针对社团经历）
    if (activeModuleId === 'team' && promptType === 'continue') {
      // 验证必填字段
      if (!validateTeamGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 20) {
        toast.error('AI续写需要至少20个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // AI改写的验证（针对社团经历）
    if (activeModuleId === 'team' && (promptType === 'professional' || promptType === 'concise' || promptType === 'detailed')) {
      // 验证必填字段
      if (!validateTeamGenerate()) {
        return;
      }
      // 验证字数
      if (!markdownContent || markdownContent.trim().length < 30) {
        toast.error('AI改写需要至少30个字的内容', {
          position: 'top-center'
        });
        return;
      }
    }

    // 中止之前的请求
    if (currentAbortFn) {
      currentAbortFn();
    }

    // 添加按钮点击视觉反馈
    setClickedButton(promptType);
    setTimeout(() => setClickedButton(null), 200);

    // 添加轻微震动效果（可选，如果觉得太明显可以注释掉）
    setIsShaking(true);
    setTimeout(() => setIsShaking(false), 300);

    setIsGenerating(true);
    setGeneratedContent('');
    generatedContentRef.current = '';
    setShowResult(true); // 立即显示结果区域
    setLastPromptType(promptType);

    // 延迟一点时间后滚动到结果区域，让用户感受到AI正在生成
    setTimeout(() => {
      if (resultAreaRef.current) {
        // 获取结果区域的位置
        const rect = resultAreaRef.current.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // 计算目标位置：结果区域顶部 + 额外向下的距离
        const targetPosition = rect.top + scrollTop + 200; // 额外向下200px

        // 平滑滚动到目标位置
        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    }, 100);

    // 生成描述文案
    let desc: string;
    if (activeModuleId === 'education') {
      desc = generateEducationPrompt(promptType);
      if (!desc) {
        setIsGenerating(false);
        return;
      }
    } else if (activeModuleId === 'work') {
      desc = generateWorkPrompt(promptType);
      if (!desc) {
        setIsGenerating(false);
        return;
      }
    } else if (activeModuleId === 'project') {
      desc = generateProjectPrompt(promptType);
      if (!desc) {
        setIsGenerating(false);
        return;
      }
    } else if (activeModuleId === 'personal_summary') {
      desc = generatePersonalSummaryPrompt(promptType);
      if (!desc) {
        setIsGenerating(false);
        return;
      }
    } else if (activeModuleId === 'research') {
      desc = generateResearchPrompt(promptType);
      if (!desc) {
        setIsGenerating(false);
        return;
      }
    } else if (activeModuleId === 'team') {
      desc = generateTeamPrompt(promptType);
      if (!desc) {
        setIsGenerating(false);
        return;
      }
    } else {
      desc = markdownContent || richTextContent || '请生成相关内容';
    }

    try {
      const abort = await aiApi.prompt(
        {
          resume_id: resumeId,
          module: getModuleName(activeModuleId),
          prompt_type: promptType,
          desc
        },
        (content) => {
          // 实时接收生成的内容
          generatedContentRef.current += content;
          setGeneratedContent(generatedContentRef.current);
        },
        () => {
          setIsGenerating(false);
          setCurrentAbortFn(null);
        },
        () => {
          // 生成完成
          setIsGenerating(false);
          setCurrentAbortFn(null);
          setShowResult(true); // 显示分析结果区域
          // 生成完成后不自动调用回调，只有点击"使用此结果"时才调用
        }
      );

      setCurrentAbortFn(() => abort);
    } catch {
      setIsGenerating(false);
      setCurrentAbortFn(null);
    }
  };

  // 处理刷新按钮点击
  const handleRefresh = () => {
    if (lastPromptType) {
      handleAIGenerate(lastPromptType);
    } else if (onRefresh) {
      onRefresh();
    }
  };

  // 处理使用结果按钮点击
  const handleUseResult = () => {
    if (showResult && generatedContentRef.current && onContentGenerated) {
      onContentGenerated(generatedContentRef.current);
    }
    if (onUseResult) {
      onUseResult();
    }
    // 隐藏结果区域
    setShowResult(false);
    setGeneratedContent('');
    generatedContentRef.current = '';
  };

  // 处理历史记录点击
  const handleHistoryClick = () => {
    // 检查登录状态
    if (!is_logged_in) {
      toast.error('请先登录后再查看历史记录', {
        position: 'top-center'
      });
      setIsLoginDialogOpen(true);
      return;
    }

    setShowHistory(true);
  };

  // 处理选择历史记录
  const handleSelectHistoryRecord = (content: string) => {
    if (onContentGenerated) {
      onContentGenerated(content);
    }
    setShowHistory(false);
  };
  return (
    <div
      className={`ai_container rounded-lg ${className} ${isShaking ? 'ai-generating-shake' : ''}`}
      style={{
        background: 'linear-gradient(280deg, #121e60 -21.04%, #735dff 103.68%, #96a1ff 103.68%)',
        padding: '6px 10px',
        borderRadius: '6px',
        position: 'relative',
      }}
    >
      {/* 历史记录 - 右上角 */}
      <div className="history_record absolute top-1.5 right-2.5">
        <button
          onClick={handleHistoryClick}
          className="text-white underline text-xs cursor-pointer hover:text-gray-200 transition-colors"
        >
          历史记录
        </button>
      </div>

      <div className="op_area">
        <div className="flex flex-wrap gap-8">
          {/* AI生成部分 */}
          <div className="ai_section">
            <div className="ai_group mb-2">
              <div className="name text-white text-sm font-medium mb-1">AI生成</div>
              <div className="ai_btns flex gap-2 flex-wrap">
                <div className="ai_item">
                  <button
                    type="button"
                    className={`ai_btn flex items-center justify-center bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30 hover:scale-105 rounded-md px-3 py-1 text-white transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed ${
                      clickedButton === 'generate' ? 'scale-95 bg-opacity-40' : ''
                    }`}
                    onClick={() => handleAIGenerate('generate')}
                    disabled={isGenerating}
                  >
                    <div className="icon mr-2">
                      {isGenerating && lastPromptType === 'generate' ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-spin">
                          <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M19 5H5C3.89543 5 3 5.89543 3 7V17C3 18.1046 3.89543 19 5 19H19C20.1046 19 21 18.1046 21 17V7C21 5.89543 20.1046 5 19 5Z" stroke="#FFB6C1" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M7 9H17" stroke="#FFB6C1" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M7 13H17" stroke="#FFB6C1" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </div>
                    <span className="txt text-sm">生成</span>
                  </button>
                </div>
                <div className="ai_item">
                  <button
                    type="button"
                    className={`ai_btn flex items-center justify-center bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30 hover:scale-105 rounded-md px-3 py-1 text-white transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed ${
                      clickedButton === 'continue' ? 'scale-95 bg-opacity-40' : ''
                    }`}
                    onClick={() => handleAIGenerate('continue')}
                    disabled={isGenerating}
                  >
                    <div className="icon mr-2">
                      {isGenerating && lastPromptType === 'continue' ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-spin">
                          <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M20 6L9 17L4 12" stroke="#FFB6C1" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </div>
                    <span className="txt text-sm">续写</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* AI改写部分 */}
          <div className="ai_section">
            <div className="ai_group mb-2">
              <div className="name text-white text-sm font-medium mb-1">AI改写</div>
              <div className="ai_btns flex gap-2 flex-wrap">
                <div className="ai_item">
                  <button
                    type="button"
                    className={`ai_btn flex items-center justify-center bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30 hover:scale-105 rounded-md px-3 py-1 text-white transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed ${
                      clickedButton === 'professional' ? 'scale-95 bg-opacity-40' : ''
                    }`}
                    onClick={() => handleAIGenerate('professional')}
                    disabled={isGenerating}
                  >
                    <div className="icon mr-2">
                      {isGenerating && lastPromptType === 'professional' ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-spin">
                          <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#87CEEB" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M12 16L16 12L12 8" stroke="#87CEEB" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M8 12H16" stroke="#87CEEB" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </div>
                    <span className="txt text-sm">更专业</span>
                  </button>
                </div>
                <div className="ai_item">
                  <button
                    type="button"
                    className={`ai_btn flex items-center justify-center bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30 hover:scale-105 rounded-md px-3 py-1 text-white transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed ${
                      clickedButton === 'concise' ? 'scale-95 bg-opacity-40' : ''
                    }`}
                    onClick={() => handleAIGenerate('concise')}
                    disabled={isGenerating}
                  >
                    <div className="icon mr-2">
                      {isGenerating && lastPromptType === 'concise' ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-spin">
                          <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M21 16V8.00002C20.9996 7.6493 20.9071 7.30483 20.7315 7.00119C20.556 6.69754 20.3037 6.44539 20 6.27002L13 2.27002C12.696 2.09449 12.3511 2.00208 12 2.00208C11.6489 2.00208 11.304 2.09449 11 2.27002L4 6.27002C3.69626 6.44539 3.44398 6.69754 3.26846 7.00119C3.09294 7.30483 3.00036 7.6493 3 8.00002V16C3.00036 16.3508 3.09294 16.6952 3.26846 16.9989C3.44398 17.3025 3.69626 17.5547 4 17.73L11 21.73C11.304 21.9056 11.6489 21.998 12 21.998C12.3511 21.998 12.696 21.9056 13 21.73L20 17.73C20.3037 17.5547 20.556 17.3025 20.7315 16.9989C20.9071 16.6952 20.9996 16.3508 21 16Z" stroke="#87CEEB" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </div>
                    <span className="txt text-sm">更凝练</span>
                  </button>
                </div>
                <div className="ai_item">
                  <button
                    type="button"
                    className={`ai_btn flex items-center justify-center bg-white bg-opacity-20 hover:bg-white hover:bg-opacity-30 hover:scale-105 rounded-md px-3 py-1 text-white transition-all duration-200 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed ${
                      clickedButton === 'detailed' ? 'scale-95 bg-opacity-40' : ''
                    }`}
                    onClick={() => handleAIGenerate('detailed')}
                    disabled={isGenerating}
                  >
                    <div className="icon mr-2">
                      {isGenerating && lastPromptType === 'detailed' ? (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-spin">
                          <path d="M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      ) : (
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M2 3H8C9.06087 3 10.0783 3.42143 10.8284 4.17157C11.5786 4.92172 12 5.93913 12 7V21C12 20.2044 11.6839 19.4413 11.1213 18.8787C10.5587 18.3161 9.79565 18 9 18H2V3Z" stroke="#87CEEB" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M22 3H16C14.9391 3 13.9217 3.42143 13.1716 4.17157C12.4214 4.92172 12 5.93913 12 7V21C12 20.2044 12.3161 19.4413 12.8787 18.8787C13.4413 18.3161 14.2044 18 15 18H22V3Z" stroke="#87CEEB" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      )}
                    </div>
                    <span className="txt text-sm">更详细</span>
                  </button>
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>



      {/* 分析结果区域 */}
      {(showAnalysisResult || showResult) && (
        <div ref={resultAreaRef} className="analysis_result mt-4">

          {/* 分析要点 */}
          <div className="analysis_points mb-2">
            <div
              className="points_container rounded-lg p-2"
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '8px'
              }}
            >
              {showResult && (
                // 显示AI生成的内容
                <div className="text-white text-sm leading-relaxed">
                  {generatedContent ? (
                    <MarkdownViewer
                      content={generatedContent}
                      className="ai-markdown-content"
                      removePadding={true}
                      inheritLineHeight={false}
                    />
                  ) : (
                    <span>正在生成中...</span>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* 底部按钮 */}
          <div className="analysis_actions flex justify-end gap-2">
            <button
              type="button"
              onClick={handleRefresh}
              className="refresh_btn flex items-center justify-center bg-cyan-400 hover:bg-cyan-500 rounded w-8 h-8 text-white text-sm transition-all duration-200 cursor-pointer"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 4V10H7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M23 20V14H17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <button
              type="button"
              onClick={handleUseResult}
              className="use_result_btn bg-pink-400 hover:bg-pink-500 rounded px-4 py-2 text-white text-sm transition-all duration-200 cursor-pointer"
            >
              使用此结果
            </button>
          </div>
        </div>
      )}

      {/* AI历史记录抽屉 */}
      <AIHistoryDrawer
        open={showHistory}
        onClose={() => setShowHistory(false)}
        onSelectRecord={handleSelectHistoryRecord}
      />

      {/* 登录对话框 */}
      <LoginDialog
        isOpen={isLoginDialogOpen}
        onClose={() => setIsLoginDialogOpen(false)}
      />
    </div>
  );
};

export default AIGenerateButtons;
